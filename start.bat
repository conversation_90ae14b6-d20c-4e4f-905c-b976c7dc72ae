@echo off
REM ChatWhiz Windows Launcher
REM Quick launcher for Windows users

echo.
echo ========================================
echo           ChatWhiz Launcher
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Show menu
:menu
echo Choose an action:
echo.
echo 1. Setup ChatWhiz (first time only)
echo 2. Index chat files
echo 3. Search chats
echo 4. Start web interface
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto index
if "%choice%"=="3" goto search
if "%choice%"=="4" goto ui
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
goto menu

:setup
echo.
echo Running setup...
python setup.py
if errorlevel 1 (
    echo Setup failed. Please check the error messages above.
    pause
    goto menu
)
echo.
echo Setup completed successfully!
pause
goto menu

:index
echo.
echo Starting indexing process...
python start.py index
if errorlevel 1 (
    echo Indexing failed. Please check the error messages above.
    pause
    goto menu
)
echo.
echo Indexing completed successfully!
pause
goto menu

:search
echo.
set /p query="Enter your search query: "
if "%query%"=="" (
    echo No query entered.
    pause
    goto menu
)
echo.
echo Searching for: %query%
python start.py search --query "%query%"
echo.
pause
goto menu

:ui
echo.
echo Starting web interface...
echo Open your browser to http://localhost:8501
echo Press Ctrl+C to stop the web interface
python start.py ui
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
