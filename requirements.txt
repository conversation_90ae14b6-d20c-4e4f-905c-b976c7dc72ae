# ChatWhiz - Semantic Chat Search System
# Core ML and NLP dependencies
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.0
InstructorEmbedding>=1.0.1
faiss-cpu>=1.7.4

# Search and indexing
rank-bm25>=0.2.2

# Data processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=1.0.0
cryptography>=41.0.0

# Web interface
streamlit>=1.25.0

# Optional LLM support
openai>=1.0.0
