# ChatWhiz 🔍

**Semantic Chat Search System** - Find messages by meaning, not just keywords!

ChatWhiz transforms your chat exports into a powerful, searchable knowledge base using state-of-the-art semantic search technology.

## ✨ Key Features

- 🧠 **Semantic Search**: Find messages by meaning using advanced AI embeddings
- 📱 **Multiple Chat Formats**: What<PERSON><PERSON><PERSON>, <PERSON>rd, JSON, CSV support
- 🎯 **Individual Message Precision**: No more concatenated results - each result is a single, relevant message
- 🚀 **Lightning Fast**: Search through 100k+ messages in milliseconds
- 🌐 **Web Interface**: Beautiful, user-friendly Streamlit interface
- 🔒 **Privacy First**: All processing happens locally on your machine
- 📊 **Smart Filtering**: Automatically filters out system messages and noise

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ChatWhiz

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.template .env
# Edit .env with your API keys (optional)
```

### 2. Index Your Chat Data

```bash
# Index a single chat file
python main.py index data/chats/my_chat.txt

# Index all files in a directory
python main.py index data/chats/

# Rebuild the entire index
python main.py index data/chats/ --rebuild
```

### 3. Search Your Chats

```bash
# Basic semantic search
python main.py search "What did John say about the meeting?"

# Keyword search with BM25
python main.py search "project deadline" --mode bm25

# Hybrid search (best of both worlds)
python main.py search "team discussion" --mode hybrid

# Get AI-powered responses (requires LLM setup)
python main.py search "Summarize our conversation about the budget" --rag
```

### 4. Web Interface

```bash
# Launch Streamlit web interface
streamlit run ui/streamlit_app.py

# Open http://localhost:8501 in your browser
```

## 📁 Supported Chat Formats

| Format | Description | Example |
|--------|-------------|---------|
| **WhatsApp** | Text export from WhatsApp | `DD/MM/YYYY, HH:MM - Sender: Message` |
| **Discord** | JSON export from DiscordChatExporter | Full message objects with metadata |
| **JSON** | Generic JSON chat format | `[{"text": "...", "sender": "...", "timestamp": "..."}]` |
| **CSV** | Comma-separated values | Columns: `text`, `sender`, `timestamp` |

## ⚙️ Configuration

Edit `config.yaml` to customize ChatWhiz:

```yaml
# Embedding Model Settings
embedding_model: "hkunlp/instructor-large"
instruction: "Represent this individual chat message for finding relevant conversations:"

# LLM Provider (optional)
llm_provider: "openai"  # none / openai / ollama
openai_api_key: "your-key-here"

# Search Settings
retrieval_mode: "semantic"  # semantic / bm25 / hybrid
top_k: 5
similarity_threshold: 0.7

# Security
store_encrypted: false
```

## 🔐 Privacy & Security

### Local-First Design
- All processing happens on your machine
- No data sent to external services (except optional LLM calls)
- Full control over your chat data

### Encryption Support
```bash
# Encrypt sensitive chat files
python main.py encrypt data/chats/private_chat.json

# Decrypt when needed
python main.py decrypt data/chats/private_chat.json.enc

# Index encrypted files (auto-decrypt during processing)
python main.py index data/chats/ --decrypt
```

## 🤖 AI Integration (Optional)

### OpenAI GPT
1. Get an API key from [OpenAI](https://platform.openai.com/)
2. Add to `.env`: `OPENAI_API_KEY=your-key-here`
3. Update `config.yaml`: `llm_provider: "openai"`

### Local Ollama
1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama2`
3. Update `config.yaml`:
   ```yaml
   llm_provider: "ollama"
   ollama_model: "llama2"
   ollama_url: "http://localhost:11434"
   ```

## 📊 Search Modes

### Semantic Search
Uses Instructor-XL embeddings to find messages with similar meaning, even if they use different words.

**Example:** Query "meeting" finds "conference", "discussion", "gathering"

### BM25 Keyword Search
Traditional keyword-based search using TF-IDF scoring.

**Example:** Query "project deadline" finds exact keyword matches

### Hybrid Search
Combines semantic and keyword search for the best of both worlds.

**Example:** Balances meaning similarity with keyword relevance

## 🛠️ Advanced Usage

### Advanced Indexing

```bash
# Rebuild entire index from scratch
python main.py index data/ --rebuild

# Index with decryption support
python main.py index data/ --decrypt
```

### Batch Operations

```bash
# Rebuild entire index
python main.py index data/ --rebuild

# Check system statistics
python main.py stats

# Process multiple file types
find data/chats -name "*.txt" -o -name "*.json" | xargs python main.py index
```

## 🏗️ Architecture

```
ChatWhiz/
├── main.py                 # CLI interface
├── config.yaml            # Configuration
├── requirements.txt        # Dependencies
├── /data/                  # Data storage
│   ├── chats/             # Raw chat files
│   ├── processed/         # Parsed chunks
│   ├── vectorstore/       # FAISS index
│   └── cache/             # Embedding cache
├── /modules/              # Core modules
│   ├── embedder.py        # Instructor-XL wrapper
│   ├── vector_store.py    # FAISS management
│   ├── loader.py          # Chat parsers
│   ├── retriever.py       # Search engine
│   ├── llm.py             # LLM integration
│   └── encryptor.py       # Encryption utilities
└── /ui/
    └── streamlit_app.py   # Web interface
```

## 🔧 Troubleshooting

### Common Issues

**"No module named 'InstructorEmbedding'"**
```bash
pip install InstructorEmbedding
```

**"FAISS not found"**
```bash
pip install faiss-cpu
# or for GPU support:
pip install faiss-gpu
```

**"Model download fails"**
- Ensure stable internet connection
- Model downloads ~1.3GB on first use
- Check available disk space

**"Search returns no results"**
- Verify data is indexed: `python main.py stats`
- Try different search modes
- Lower similarity threshold in config

### Performance Tips

- Use GPU if available for faster embedding
- Use `flat` index for small datasets, `ivf` for large ones
- Enable embedding cache for repeated searches
- Lower similarity threshold for more results

## 📝 License

This project is open source. See LICENSE file for details.

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

## 🙏 Acknowledgments

- [Instructor-XL](https://huggingface.co/hkunlp/instructor-xl) for state-of-the-art embeddings
- [FAISS](https://github.com/facebookresearch/faiss) for efficient similarity search
- [Streamlit](https://streamlit.io/) for the beautiful web interface

---

**Made with ❤️ for better chat search experiences**
