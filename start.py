#!/usr/bin/env python3
"""
ChatWhiz Startup Script
Quick launcher for ChatWhiz with automatic setup and validation.
"""

import os
import sys
import subprocess
import argparse

def check_setup():
    """Check if ChatWhiz is properly set up."""
    print("🔍 Checking ChatWhiz setup...")
    
    # Check if config exists
    if not os.path.exists("config.yaml"):
        print("❌ config.yaml not found. Run setup.py first.")
        return False
    
    # Check if required directories exist
    required_dirs = ["data/chats", "data/cache", "data/processed", "data/vectorstore"]
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Directory missing: {directory}")
            return False
    
    # Check if dependencies are installed
    try:
        import torch
        import InstructorEmbedding
        import streamlit
        print("✅ Setup verification passed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Run setup.py to install dependencies.")
        return False

def run_indexing():
    """Run the indexing process."""
    print("🔄 Starting indexing process...")
    
    # Check if chat files exist
    chat_files = []
    if os.path.exists("data/chats"):
        for file in os.listdir("data/chats"):
            if file.endswith(('.txt', '.json', '.csv')):
                chat_files.append(file)
    
    if not chat_files:
        print("❌ No chat files found in data/chats/")
        print("Please add your chat export files to the data/chats/ directory")
        return False
    
    print(f"📁 Found {len(chat_files)} chat files: {', '.join(chat_files)}")
    
    # Run indexing
    cmd = f"{sys.executable} main.py index data/chats/ --rebuild"
    print(f"🔄 Running: {cmd}")
    
    try:
        subprocess.run(cmd, shell=True, check=True)
        print("✅ Indexing completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Indexing failed: {e}")
        return False

def run_search(query):
    """Run a search query."""
    cmd = f"{sys.executable} main.py search \"{query}\" --mode semantic --top-k 5"
    print(f"🔍 Running: {cmd}")
    
    try:
        subprocess.run(cmd, shell=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Search failed: {e}")
        return False

def run_ui():
    """Start the Streamlit web interface."""
    print("🌐 Starting ChatWhiz web interface...")
    
    cmd = f"{sys.executable} run_ui.py"
    print(f"🔄 Running: {cmd}")
    
    try:
        subprocess.run(cmd, shell=True, check=True)
    except KeyboardInterrupt:
        print("\n👋 ChatWhiz web interface stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Web interface failed: {e}")

def main():
    """Main startup function."""
    parser = argparse.ArgumentParser(description="ChatWhiz Startup Script")
    parser.add_argument("action", choices=["setup", "index", "search", "ui"], 
                       help="Action to perform")
    parser.add_argument("--query", type=str, help="Search query (for search action)")
    parser.add_argument("--force", action="store_true", help="Force action even if setup check fails")
    
    args = parser.parse_args()
    
    print("🚀 ChatWhiz Startup")
    print("=" * 40)
    
    # Setup action doesn't need validation
    if args.action == "setup":
        print("🔄 Running setup...")
        try:
            subprocess.run(f"{sys.executable} setup.py", shell=True, check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ Setup failed: {e}")
            sys.exit(1)
        return
    
    # Check setup for other actions
    if not args.force and not check_setup():
        print("\n💡 Run 'python start.py setup' to set up ChatWhiz first")
        sys.exit(1)
    
    # Execute the requested action
    if args.action == "index":
        if not run_indexing():
            sys.exit(1)
    
    elif args.action == "search":
        if not args.query:
            print("❌ Search query required. Use --query \"your search terms\"")
            sys.exit(1)
        if not run_search(args.query):
            sys.exit(1)
    
    elif args.action == "ui":
        run_ui()
    
    print("\n✅ Action completed successfully!")

if __name__ == "__main__":
    main()
