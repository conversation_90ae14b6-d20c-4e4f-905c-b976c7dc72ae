# ChatWhiz Configuration

# Embedding Model Settings
embedding_model: "hkunlp/instructor-large"
instruction: "Represent this individual chat message for finding relevant conversations:"
device: "auto"                   # auto / cpu / cuda
# No chunking - each message is indexed individually

# LLM Provider Settings
llm_provider: "none"          # none / openai / ollama
openai_api_key: ""
ollama_model: "llama2"
ollama_url: "http://localhost:11434"

# Security Settings
store_encrypted: false

# Retrieval Settings
retrieval_mode: "semantic"    # semantic / bm25 / hybrid
top_k: 5
similarity_threshold: 0.3

# Data Paths
data_dir: "data"
chats_dir: "data/chats"
processed_dir: "data/processed"
vectorstore_dir: "data/vectorstore"
cache_dir: "data/cache"

# UI Settings
streamlit_port: 8501
streamlit_host: "localhost"
