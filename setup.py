#!/usr/bin/env python3
"""
ChatWhiz Setup Script
Prepares the environment and installs dependencies for ChatWhiz semantic chat search system.
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def create_directories():
    """Create necessary directories."""
    directories = [
        "data/cache",
        "data/processed", 
        "data/vectorstore",
        "data/chats"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_dependencies():
    """Install Python dependencies."""
    print("🔄 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    return True

def verify_installation():
    """Verify that key dependencies are installed correctly."""
    print("🔄 Verifying installation...")
    
    try:
        import torch
        import transformers
        import InstructorEmbedding
        import faiss
        import streamlit
        import yaml
        print("✅ All core dependencies verified")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def create_sample_config():
    """Create a sample configuration if none exists."""
    if not os.path.exists("config.yaml"):
        print("🔄 Creating sample configuration...")
        sample_config = """# ChatWhiz Configuration

# Embedding Model Settings
embedding_model: "hkunlp/instructor-large"
instruction: "Represent this individual chat message for finding relevant conversations:"
device: "auto"                   # auto / cpu / cuda
# No chunking - each message is indexed individually

# LLM Provider Settings (Optional)
llm_provider: "none"          # none / openai / ollama
openai_api_key: ""
ollama_model: "llama2"
ollama_url: "http://localhost:11434"

# Security Settings
store_encrypted: false

# Retrieval Settings
retrieval_mode: "semantic"    # semantic / bm25 / hybrid
top_k: 5
similarity_threshold: 0.3

# Data Paths
data_dir: "data"
chats_dir: "data/chats"
processed_dir: "data/processed"
vectorstore_dir: "data/vectorstore"
cache_dir: "data/cache"

# UI Settings
streamlit_port: 8501
streamlit_host: "localhost"
"""
        with open("config.yaml", "w") as f:
            f.write(sample_config)
        print("✅ Created config.yaml")

def main():
    """Main setup function."""
    print("🚀 ChatWhiz Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check the error messages above.")
        sys.exit(1)
    
    # Verify installation
    print("\n🔍 Verifying installation...")
    if not verify_installation():
        print("❌ Installation verification failed. Please check the error messages above.")
        sys.exit(1)
    
    # Create sample config
    print("\n⚙️ Setting up configuration...")
    create_sample_config()
    
    print("\n" + "=" * 50)
    print("✅ ChatWhiz setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Place your chat files in the 'data/chats/' directory")
    print("2. Run: python main.py index data/chats/ --rebuild")
    print("3. Run: python main.py search \"your query\" --mode semantic")
    print("4. Or start the web UI: python run_ui.py")
    print("\n📖 For more information, see README.md")

if __name__ == "__main__":
    main()
