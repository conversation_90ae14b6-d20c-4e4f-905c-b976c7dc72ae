#!/usr/bin/env python3
"""
ChatWhiz Validation Script
Tests that the deployment is working correctly.
"""

import os
import sys
import importlib
import subprocess

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current: {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def test_dependencies():
    """Test that all required dependencies are installed."""
    print("\n📦 Testing dependencies...")
    
    required_packages = [
        'torch',
        'transformers', 
        'InstructorEmbedding',
        'faiss',
        'streamlit',
        'yaml',
        'numpy',
        'pandas',
        'rank_bm25'
    ]
    
    failed = []
    for package in required_packages:
        try:
            if package == 'yaml':
                importlib.import_module('yaml')
            elif package == 'faiss':
                importlib.import_module('faiss')
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed.append(package)
    
    if failed:
        print(f"\n❌ Missing dependencies: {', '.join(failed)}")
        print("Run: python setup.py")
        return False
    
    print("✅ All dependencies installed")
    return True

def test_file_structure():
    """Test that required files and directories exist."""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'main.py',
        'run_ui.py', 
        'config.yaml',
        'requirements.txt',
        'modules/__init__.py',
        'modules/embedder.py',
        'modules/loader.py',
        'modules/retriever.py',
        'modules/vector_store.py',
        'ui/streamlit_app.py'
    ]
    
    required_dirs = [
        'data',
        'data/chats',
        'data/cache',
        'data/processed',
        'data/vectorstore',
        'modules',
        'ui'
    ]
    
    missing = []
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    # Check directories
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing.append(dir_path)
        else:
            print(f"✅ {dir_path}/")
    
    if missing:
        print(f"\n❌ Missing files/directories: {', '.join(missing)}")
        return False
    
    print("✅ All required files and directories present")
    return True

def test_config():
    """Test configuration file."""
    print("\n⚙️ Testing configuration...")
    
    try:
        import yaml
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        required_keys = [
            'embedding_model',
            'similarity_threshold',
            'data_dir',
            'chats_dir'
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
            else:
                print(f"✅ {key}: {config[key]}")
        
        if missing_keys:
            print(f"❌ Missing config keys: {', '.join(missing_keys)}")
            return False
        
        print("✅ Configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_modules():
    """Test that core modules can be imported."""
    print("\n🔧 Testing core modules...")
    
    modules_to_test = [
        'modules.embedder',
        'modules.loader',
        'modules.retriever', 
        'modules.vector_store'
    ]
    
    failed = []
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed.append(module)
    
    if failed:
        print(f"\n❌ Failed to import: {', '.join(failed)}")
        return False
    
    print("✅ All core modules importable")
    return True

def test_sample_data():
    """Check if sample data exists."""
    print("\n📄 Testing sample data...")
    
    sample_files = [
        'data/chats/sample_chat.json',
        'data/chats/small_sample.txt'
    ]
    
    found = []
    for file_path in sample_files:
        if os.path.exists(file_path):
            found.append(file_path)
            print(f"✅ {file_path}")
    
    if not found:
        print("⚠️ No sample data found in data/chats/")
        print("Add your chat files to data/chats/ to test indexing")
    else:
        print(f"✅ Found {len(found)} sample files")
    
    return True

def test_cli():
    """Test CLI functionality."""
    print("\n💻 Testing CLI...")
    
    try:
        # Test help command
        result = subprocess.run([sys.executable, 'main.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ CLI help command works")
        else:
            print(f"❌ CLI help failed: {result.stderr}")
            return False
        
        # Test stats command (should work even without data)
        result = subprocess.run([sys.executable, 'main.py', 'stats'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ CLI stats command works")
        else:
            print("⚠️ CLI stats command failed (expected if no data indexed)")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("🔍 ChatWhiz Deployment Validation")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_file_structure,
        test_config,
        test_modules,
        test_sample_data,
        test_cli
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ChatWhiz is ready for deployment!")
        print("\n📋 Next steps:")
        print("1. Add chat files to data/chats/")
        print("2. Run: python start.py index")
        print("3. Run: python start.py ui")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
